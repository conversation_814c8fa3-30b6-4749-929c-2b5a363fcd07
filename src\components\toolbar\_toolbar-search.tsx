"use client"

import * as React from "react"
import { SearchIcon } from "lucide-react"
import { useQueryState } from "nuqs"

import { DynamicObject } from "@/types/globals"
import { cn } from "@/lib/utils"
import { useDebouncedCallback } from "@/hooks/use-debounced-callback"

import { Input } from "../ui/input"
import { useToolbar } from "./_toolbar-provider"
import { _getSearchStateParser } from "./_toolbar-search-parser"

type _SearchItem<T extends DynamicObject = DynamicObject> = {
  id: Extract<keyof T, string>
  placeholder?: string
}

type SearchProps<T extends DynamicObject = DynamicObject> = {
  item: _SearchItem<T>
  shallow?: boolean
  debounceMs?: number
  className?: string
}

function Search<T extends DynamicObject = DynamicObject>({
  item,
  shallow = false,
  debounceMs = 300,
  className,
}: SearchProps<T>) {
  const { startTransition } = useToolbar()
  const [value, setValue] = useQueryState(
    "search",
    _getSearchStateParser(item.id)
      .withDefault({ id: item.id, value: "" })
      .withOptions({
        startTransition,
        clearOnDefault: true,
        shallow,
        throttleMs: debounceMs,
      })
  )

  const debouncedSetValue = useDebouncedCallback(setValue, debounceMs)

  const onSearchChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      debouncedSetValue({ id: item.id, value: event.target.value })
    },
    [debouncedSetValue, item.id]
  )

  return (
    <div className={cn("relative w-full", className)}>
      <Input
        aria-label="Search"
        type="search"
        placeholder={item.placeholder}
        defaultValue={value?.value}
        onChange={onSearchChange}
        className={cn(
          // base
          "h-8 max-h-full w-full ps-8",
          // hide cancel button and decoration
          "[&::-webkit-search-cancel-button]:hidden [&::-webkit-search-decoration]:hidden"
        )}
      />
      <div
        className={cn(
          // base
          "pointer-events-none absolute start-2 bottom-0 flex h-full items-center justify-center",
          // text color
          "text-muted-foreground/60"
        )}
      >
        <SearchIcon className="size-5 shrink-0" aria-hidden="true" />
      </div>
    </div>
  )
}

export { Search as _Search }
