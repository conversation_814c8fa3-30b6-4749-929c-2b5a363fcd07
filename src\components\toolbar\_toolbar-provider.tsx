"use client"

import React from "react"

import { _ToolbarProps } from "./_toolbar"
import { _GetToolbarDictionaryResult } from "./_toolbar-lib"

type ToolbarContextValue = Omit<_ToolbarProps, "children" | "className"> & {
  dictionaries: _GetToolbarDictionaryResult
}

const ToolbarContext = React.createContext<ToolbarContextValue | null>(null)

function ToolbarProvider({
  value,
  children,
}: {
  value: ToolbarContextValue
  children: React.ReactNode
}) {
  return <ToolbarContext value={value}>{children}</ToolbarContext>
}

export function useToolbar() {
  const context = React.useContext(ToolbarContext)
  if (!context) {
    throw new Error("useToolbarContext must be used within a ToolbarProvider")
  }
  return context
}

export { ToolbarProvider as _ToolbarProvider }
