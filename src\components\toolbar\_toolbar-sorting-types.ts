import { DynamicObject } from "@/types/globals"

import { _SortingState } from "./_toolbar-sorting-lib"

export type _SortDirection = "asc" | "desc"

type SortingItem<T extends DynamicObject = DynamicObject> = {
  /** مفتاح كائن البيانات الذي تريد تمكين استخدامه للفرز */
  id: Extract<keyof T, string>
  /** العنوان الذي سيظهر في القائمة */
  label: string
}

export type _OnSortRemove = (sortId: string) => void
export type _OnSortUpdate = (
  sortId: string,
  updates: Partial<_SortingState>
) => void

export interface _SortingItemProps {
  sort: _SortingState
  sortItemId: string
  items: { id: string; label: string }[]
  onSortUpdate: _OnSortUpdate
  onSortRemove: _OnSortRemove
}

export type _SortingProps<T extends DynamicObject = DynamicObject> = {
  /**
   * مصفوفة العناصر التي تريد تمكين استخدامها للفرز.
   */
  items: SortingItem<T>[]
  className?: string
}
