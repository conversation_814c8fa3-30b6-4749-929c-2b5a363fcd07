import React from "react"
import {
  createSearchParamsCache,
  parseAsStringEnum,
  SearchParams,
} from "nuqs/server"

import { Locale } from "@/types/globals"

import _toolbarDictionariesAr from "./_toolbar-dictionaries-ar"
import _toolbarDictionariesEn from "./_toolbar-dictionaries-en"
import {
  _getFilterQueryParser,
  _getFiltersStateParser,
} from "./_toolbar-filter-parser"
import {
  _getSearchStateParser,
  _searchQueryParser,
} from "./_toolbar-search-parser"
import {
  _getSortingQueryParser,
  _getSortingStateParser,
} from "./_toolbar-sorting-lib"

type _GetToolbarDictionaryResult = ReturnType<typeof _getToolbarDictionary>

const toolbarDictionaries = {
  en: _toolbarDictionariesEn,
  ar: _toolbarDictionariesAr,
}

const _getToolbarDictionary = (locale: Locale) => {
  return toolbarDictionaries[locale]
}

const _getToolbarParser = React.cache(
  async (searchParams: Promise<SearchParams> | SearchParams) => {
    const searchParamsCache = createSearchParamsCache({
      filters: _getFiltersStateParser().withDefault([]),
      join: parseAsStringEnum(["AND", "OR"]).withDefault("AND"),
      search: _getSearchStateParser().withDefault({}),
      sort: _getSortingStateParser().withDefault([]),
    })

    const searchParamsCacheParsed = searchParamsCache.parse(await searchParams)

    const filters = _getFilterQueryParser({
      filters: searchParamsCacheParsed.filters,
      join: searchParamsCacheParsed.join,
    })

    const search = _searchQueryParser(searchParamsCacheParsed.search)
    const sorting = _getSortingQueryParser(searchParamsCacheParsed.sort)

    const where = { ...filters, ...search }
    const orderBy = sorting

    return { where, orderBy }
  }
)

export {
  _getToolbarParser,
  _getToolbarDictionary,
  type _GetToolbarDictionaryResult,
}
