import React, { useMemo } from "react"

import { Locale } from "@/types/globals"

import { _getToolbarDictionary } from "./_toolbar-lib"
import { _ToolbarProvider } from "./_toolbar-provider"

export interface _ToolbarProps {
  className?: string
  children?: React.ReactNode

  startTransition?: React.TransitionStartFunction
  dir?: "rtl" | "ltr"
  locale?: Locale
}

function Toolbar({
  children,
  className,
  locale = "ar",
  startTransition,
  dir = "rtl",
}: _ToolbarProps) {
  const dictionaries = useMemo(() => _getToolbarDictionary(locale), [locale])

  return (
    <_ToolbarProvider value={{ startTransition, dictionaries, dir }}>
      <div className={className} dir={dir}>
        {children}
      </div>
    </_ToolbarProvider>
  )
}

export { Toolbar as _Toolbar }
