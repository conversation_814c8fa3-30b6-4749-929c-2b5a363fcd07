import React from "react"

import { DynamicObject } from "@/types/globals"

export type _FilterVariant =
  | "text"
  | "number"
  | "numberRange"
  | "date"
  | "dateRange"
  | "boolean"
  | "select"
  | "multiSelect"

type TextItem<K extends string = string> = {
  /**
   * المفتاح المستخدم لتحديد الحقل داخل كائن البيانات المراد تصفيته.
   */
  id: K
  /**
   * اسم العرض (Label) الذي يظهر للمستخدم في واجهة التصفية.
   */
  label: string
  /**
   * نوع الفلتر المستخدم لهذا الحقل.
   *
   * القيم المدعومة:
   * - text: فلترة نصية (مثل "يحتوي على" أو "يساوي").
   * - number: فلترة رقمية (مثل "أكبر من" أو "أقل من").
   * - date: فلترة حسب التاريخ (مثل "قبل" أو "بعد").
   * - boolean: فلترة منطقية (true / false).
   * - select: اختيار عنصر واحد من قائمة محددة مسبقًا.
   * - multiSelect: اختيار عناصر متعددة من قائمة محددة مسبقًا.
   */
  variant: "text"
  placeholder?: string
}

type NumericItem<K extends string = string> = {
  /**
   * المفتاح المستخدم لتحديد الحقل داخل كائن البيانات المراد تصفيته.
   */
  id: K
  /**
   * اسم العرض (Label) الذي يظهر للمستخدم في واجهة التصفية.
   */
  label: string
  /**
   * نوع الفلتر المستخدم لهذا الحقل.
   *
   * القيم المدعومة:
   * - text: فلترة نصية (مثل "يحتوي على" أو "يساوي").
   * - number: فلترة رقمية (مثل "أكبر من" أو "أقل من").
   * - date: فلترة حسب التاريخ (مثل "قبل" أو "بعد").
   * - boolean: فلترة منطقية (true / false).
   * - select: اختيار عنصر واحد من قائمة محددة مسبقًا.
   * - multiSelect: اختيار عناصر متعددة من قائمة محددة مسبقًا.
   */
  variant: "number"
  placeholder?: string
  /**
   * نطاق القيمة المدخلة.
   *
   * يمكن تحديد الحد الأدنى والحد الأقصى للقيمة المدخلة.
   * ولن يستطيع المستخدم إدخال قيم خارج هذا النطاق.
   */
  range?: { min?: number; max?: number }
  /**
   * يحدد مقدار تزايد ونقص القيمة المدخلة في النقره الواحدة على ازرار حقل الرقم.
   */
  step?: number
  /**
   * وحدة القياس المستخدمة لعرض القيمة المدخلة.
   * تعرض بجانب الحقل للإشارة الى وحدة القياس المستخدمة على سبيل المثال: ($) لوحدة عملة الدولار.
   */
  unit?: string
}

type DateItem<K extends string = string> = {
  /**
   * المفتاح المستخدم لتحديد الحقل داخل كائن البيانات المراد تصفيته.
   */
  id: K
  /**
   * اسم العرض (Label) الذي يظهر للمستخدم في واجهة التصفية.
   */
  label: string
  /**
   * نوع الفلتر المستخدم لهذا الحقل.
   *
   * القيم المدعومة:
   * - text: فلترة نصية (مثل "يحتوي على" أو "يساوي").
   * - number: فلترة رقمية (مثل "أكبر من" أو "أقل من").
   * - date: فلترة حسب التاريخ (مثل "قبل" أو "بعد").
   * - boolean: فلترة منطقية (true / false).
   * - select: اختيار عنصر واحد من قائمة محددة مسبقًا.
   * - multiSelect: اختيار عناصر متعددة من قائمة محددة مسبقًا.
   */
  variant: "date"
  /**
   * نطاق القيمة المدخلة.
   *
   * يمكن تحديد الحد الأدنى والحد الأقصى للقيمة المدخلة.
   * ولن يستطيع المستخدم إدخال قيم خارج هذا النطاق.
   *
   * ملاحضة بشأن تنسيق التاريخ:
   * يجب ان يكون التنسيق بصيغ ISO 8601.
   *
   * يمكنك معرفة المزيد عن تنسيق ISO 8601 في الرابط التالي:
   * @see https://en.wikipedia.org/wiki/ISO_8601
   */
  range?: { min?: Date | string; max?: Date | string }
  placeholder?: string
}

type BooleanItem<K extends string = string> = {
  /**
   * المفتاح المستخدم لتحديد الحقل داخل كائن البيانات المراد تصفيته.
   */
  id: K
  /**
   * اسم العرض (Label) الذي يظهر للمستخدم في واجهة التصفية.
   */
  label: string
  /**
   * نوع الفلتر المستخدم لهذا الحقل.
   *
   * القيم المدعومة:
   * - text: فلترة نصية (مثل "يحتوي على" أو "يساوي").
   * - number: فلترة رقمية (مثل "أكبر من" أو "أقل من").
   * - date: فلترة حسب التاريخ (مثل "قبل" أو "بعد").
   * - boolean: فلترة منطقية (true / false).
   * - select: اختيار عنصر واحد من قائمة محددة مسبقًا.
   * - multiSelect: اختيار عناصر متعددة من قائمة محددة مسبقًا.
   */
  variant: "boolean"
}

type SelectItem<K extends string = string, V = unknown> = {
  /**
   * المفتاح المستخدم لتحديد الحقل داخل كائن البيانات المراد تصفيته.
   */
  id: K
  /**
   * اسم العرض (Label) الذي يظهر للمستخدم في واجهة التصفية.
   */
  label: string
  /**
   * نوع الفلتر المستخدم لهذا الحقل.
   *
   * القيم المدعومة:
   * - text: فلترة نصية (مثل "يحتوي على" أو "يساوي").
   * - number: فلترة رقمية (مثل "أكبر من" أو "أقل من").
   * - date: فلترة حسب التاريخ (مثل "قبل" أو "بعد").
   * - boolean: فلترة منطقية (true / false).
   * - select: اختيار عنصر واحد من قائمة محددة مسبقًا.
   * - multiSelect: اختيار عناصر متعددة من قائمة محددة مسبقًا.
   */
  variant: "select" | "multiSelect"
  /**
   * قائمة الخيارات المتاحة للاختيار من بينها.
   */
  options: {
    /**
     * النص الظاهر للمستخدم لتمثيل هذا الخيار في واجهة التصفية.
     */
    label: string
    /**
     * القيمة الفعلية التي سيتم استخدامها للتصفية.
     */
    value: V
    /**
     * (اختياري) عدد العناصر المرتبطة بهذا الخيار.
     * يتم عرض هذا العدد بجانب اسم الخيار في واجهة التصفية.
     */
    count?: number
    /**
     * (اختياري) مكون React يعرض كأيقونة بجانب اسم الخيار.
     * يجب ان يكون المكون قادر على استقبال خاصية className.
     */
    icon?: (props: {
      className?: string
      [K: string]: any
    }) => React.JSX.Element
  }[]
  placeholder?: string
}

export type _FilterItemDef<T extends DynamicObject = DynamicObject> = {
  [K in keyof T]:
    | TextItem<Extract<K, string>>
    | NumericItem<Extract<K, string>>
    | DateItem<Extract<K, string>>
    | BooleanItem<Extract<K, string>>
    | SelectItem<Extract<K, string>, T[K]>
}[keyof T]

export type _FilterProps<T extends DynamicObject = DynamicObject> = {
  /**
   * قائمة بيانات العناصر التي سيتم استخداها للتصفية.
   */
  items: _FilterItemDef<T>[]
  /**
   * إذا كانت القيمة true، سيتم تحديث عنوان الرابط (URL) باستخدام التصفية
   * دون إعادة تحميل الصفحة (shallow routing).
   *
   * @default false
   */
  shallow?: boolean
  /**
   * مدة التأخير (بالملي ثانية) قبل تطبيق الفلتر وتحديث القيمة في عنوان الرابط (URL).
   * يُستخدم هذا لتقليل عدد مرات التحديث عند الكتابة أو التفاعل مع الفلتر.
   *
   * @default 300
   */
  debounceMs?: number
  /**
   * يتم تطبيق الخصائص على عنصر الزر الخاص بفتح نافذة القائمة فقط.
   */
  className?: string
}
