import { createParser } from "nuqs/server"

import { _SearchState, _searchStateSchema } from "./_toolbar-search-lib"

export const _getSearchStateParser = (itemKey?: string) => {
  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = _searchStateSchema.safeParse(parsed)

        if (!result.success) return null

        if (itemKey && result.data?.id !== itemKey) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) => a.id === b.id && a.value === b.value,
  })
}

export const _searchQueryParser = (search: _SearchState) => {
  if (!search.id || !search.value) return {} // no search value or id provided, return null to skip search query parsing.
  return { [search.id]: { contains: search.value, mode: "insensitive" } }
}
