import { z } from "zod/v4"

import { _FilterVariant } from "./_toolbar-filter-types"
import { _GetToolbarDictionaryResult } from "./_toolbar-lib"

const textOperators = [
  "iLike",
  "notILike",
  "eq",
  "ne",
  "isEmpty",
  "isNotEmpty",
] as const
const numericOperators = [
  "eq",
  "ne",
  "lt",
  "lte",
  "gt",
  "gte",
  "isBetween",
  "isEmpty",
  "isNotEmpty",
] as const
const dateOperators = [
  "eq",
  "ne",
  "lt",
  "lte",
  "gt",
  "gte",
  "isBetween",
  "isEmpty",
  "isNotEmpty",
] as const
const multiSelectOperators = [
  "inArray",
  "notInArray",
  "isEmpty",
  "isNotEmpty",
] as const
const selectOperators = ["eq", "ne", "isEmpty", "isNotEmpty"] as const
const booleanOperators = ["eq", "ne"] as const

export const _getDefaultFilterOperator = (filterVariant: _FilterVariant) => {
  switch (filterVariant) {
    case "text":
      return "iLike"
    case "number":
      return "eq"
    case "date":
      return "eq"
    case "boolean":
      return "eq"
    case "select":
      return "eq"
    case "multiSelect":
      return "inArray"
    default:
      return "eq"
  }
}

export const _getFilterOperators = (
  filterVariant: _FilterVariant,
  dict: _GetToolbarDictionaryResult["filter"]["filterItem"]["operatorSelector"]["operators"]
) => {
  switch (filterVariant) {
    case "text":
      return textOperators.map((o) => ({
        value: o,
        label: dict.text[o],
      }))
    case "number":
    case "numberRange":
      return numericOperators.map((o) => ({
        value: o,
        label: dict.number[o],
      }))
    case "date":
    case "dateRange":
      return dateOperators.map((o) => ({
        value: o,
        label: dict.date[o],
      }))
    case "boolean":
      return booleanOperators.map((o) => ({
        value: o,
        label: dict.boolean[o],
      }))
    case "select":
      return selectOperators.map((o) => ({
        value: o,
        label: dict.select[o],
      }))
    case "multiSelect":
      return multiSelectOperators.map((o) => ({
        value: o,
        label: dict.multiSelect[o],
      }))
  }
}

const _filterTextStateSchema = z.object({
  id: z.string(),
  value: z.string().min(1),
  variant: z.enum(["text"]), // text
  operator: z.enum(textOperators),
  filterId: z.string(),
})

const _filterNumericStateSchema = _filterTextStateSchema.extend({
  variant: z.enum(["number"]), // number
  operator: z.enum(numericOperators).exclude(["isBetween"]),
  value: z.string().min(1),
})

const _filterNumericRangeStateSchema = _filterTextStateSchema.extend({
  variant: z.enum(["numberRange"]), // numberRange
  operator: z.enum(["isBetween"]),
  value: z.array(z.string()).min(2),
})

const _filterDateStateSchema = _filterTextStateSchema.extend({
  variant: z.enum(["date"]), // date
  operator: z.enum(dateOperators).exclude(["isBetween"]),
  value: z.string().min(1),
})

const _filterDateRangeStateSchema = _filterTextStateSchema.extend({
  variant: z.enum(["dateRange"]), // dateRange
  operator: z.enum(["isBetween"]),
  value: z.array(z.string()).min(2),
})

const _filterBooleanStateSchema = _filterTextStateSchema.extend({
  variant: z.enum(["boolean"]), // boolean
  operator: z.enum(booleanOperators),
  value: z.stringbool(),
})

const _filterSelectStateSchema = _filterTextStateSchema.extend({
  variant: z.enum(["select"]), // select
  operator: z.enum(selectOperators),
  value: z.string().min(1),
})

const _filterMultiSelectStateSchema = _filterTextStateSchema.extend({
  variant: z.enum(["multiSelect"]), // multiSelect
  operator: z.enum(multiSelectOperators),
  value: z.union([z.array(z.string()).min(1), z.enum(["empty", "notEmpty"])]),
})

const _filterJoinStateSchema = z.enum(["AND", "OR"])

const _filterStateSchema = z.discriminatedUnion("variant", [
  _filterTextStateSchema,
  _filterNumericStateSchema,
  _filterNumericRangeStateSchema,
  _filterDateStateSchema,
  _filterDateRangeStateSchema,
  _filterBooleanStateSchema,
  _filterSelectStateSchema,
  _filterMultiSelectStateSchema,
])

type _FilterState = z.infer<typeof _filterStateSchema>
type _FilterJoinState = z.infer<typeof _filterJoinStateSchema>

export { _filterStateSchema, type _FilterState, type _FilterJoinState }
