import * as React from "react"
import { endOfDay, startOfDay } from "date-fns"
import { createParser } from "nuqs/server"
import { z } from "zod/v4"

import {
  _FilterJoinState,
  _FilterState,
  _filterStateSchema,
} from "./_toolbar-filter-lib"

export const _getFiltersStateParser = (itemIds?: string[] | Set<string>) => {
  const validKeys = itemIds
    ? itemIds instanceof Set
      ? itemIds
      : new Set(itemIds)
    : null

  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = z.array(_filterStateSchema).safeParse(parsed)

        if (!result.success) return null

        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) =>
      a.length === b.length &&
      a.every(
        (filter, index) =>
          filter.id === b[index]?.id &&
          filter.value === b[index]?.value &&
          filter.variant === b[index]?.variant &&
          filter.operator === b[index]?.operator
      ),
  })
}

/**
 * parser to prisam query whare query
 */
const textVariantParser = (filter: _FilterState & { variant: "text" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "iLike": {
      return { [id]: { contains: value, mode: "insensitive" } }
    }
    case "notILike": {
      return { NOT: { [id]: { contains: value, mode: "insensitive" } } }
    }
    case "eq": {
      return { [id]: value }
    }
    case "ne": {
      return { NOT: { [id]: value } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const numericVariantParser = (filter: _FilterState & { variant: "number" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [id]: Number(value) }
    }
    case "ne": {
      return { NOT: { [id]: Number(value) } }
    }
    case "lt": {
      return { [id]: { lt: Number(value) } }
    }
    case "lte": {
      return { [id]: { lte: Number(value) } }
    }
    case "gt": {
      return { [id]: { gt: Number(value) } }
    }
    case "gte": {
      return { [id]: { gte: Number(value) } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const numericRangeVariantParser = (
  filter: _FilterState & { variant: "numberRange" }
) => {
  const id = filter.id
  const value = filter.value

  return { [id]: { gt: Number(value[0]), lte: Number(value[1]) } }
}

const dateVariantParser = (filter: _FilterState & { variant: "date" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator
  const date = new Date(Number(value))
  const startDay = startOfDay(date)
  const endDay = endOfDay(date)

  switch (operator) {
    case "eq": {
      return { [id]: { gte: startDay, lte: endDay } }
    }
    case "ne": {
      return { NOT: { [id]: { gte: startDay, lte: endDay } } }
    }
    case "lt": {
      return { [id]: { lt: startDay } }
    }
    case "lte": {
      return { [id]: { lte: endDay } }
    }
    case "gt": {
      return { [id]: { gt: endDay } }
    }
    case "gte": {
      return { [id]: { gte: startDay } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const dateRangeVariantParser = (
  filter: _FilterState & { variant: "dateRange" }
) => {
  const id = filter.id
  const value = filter.value
  const min = startOfDay(new Date(Number(value[0])))
  const max = endOfDay(new Date(Number(value[1])))

  return {
    [id]: { gte: min, lte: max },
  }
}

const booleanVariantParser = (
  filter: _FilterState & { variant: "boolean" }
) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [id]: Boolean(value) }
    }
    case "ne": {
      return { NOT: { [id]: Boolean(value) } }
    }
    default:
      return undefined
  }
}

const selectVariantParser = (filter: _FilterState & { variant: "select" }) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "eq": {
      return { [id]: value }
    }
    case "ne": {
      return { NOT: { [id]: value } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

const multiSelectVariantParser = (
  filter: _FilterState & { variant: "multiSelect" }
) => {
  const id = filter.id
  const value = filter.value
  const operator = filter.operator

  switch (operator) {
    case "inArray": {
      return { [id]: { in: value } }
    }
    case "notInArray": {
      return { NOT: { [id]: { in: value } } }
    }
    // case "isEmpty": {
    //   return { [id]: null }
    // }
    // case "isNotEmpty": {
    //   return { [id]: { not: null } }
    // }
    default:
      return undefined
  }
}

export const _getFilterQueryParser = React.cache(
  ({ filters, join }: { filters: _FilterState[]; join: _FilterJoinState }) => {
    const parsedFilters = filters
      .map((filter) => {
        switch (filter.variant) {
          case "text":
            return textVariantParser(filter)
          case "number":
            return numericVariantParser(filter)
          case "numberRange":
            return numericRangeVariantParser(filter)
          case "date":
            return dateVariantParser(filter)
          case "dateRange":
            return dateRangeVariantParser(filter)
          case "boolean":
            return booleanVariantParser(filter)
          case "select":
            return selectVariantParser(filter)
          case "multiSelect":
            return multiSelectVariantParser(filter)
          default:
            return undefined
        }
      })
      .filter(Boolean)

    return parsedFilters.length > 0 ? { [join]: parsedFilters } : {}
  }
)
