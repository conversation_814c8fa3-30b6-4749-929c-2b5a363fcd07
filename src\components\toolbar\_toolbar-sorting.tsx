import React from "react"
import { ArrowDownUp } from "lucide-react"
import { useQueryState } from "nuqs"

import { DynamicObject } from "@/types/globals"
import { cn } from "@/lib/utils"

import { Badge } from "../ui/badge"
import { <PERSON><PERSON> } from "../ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover"
import { Sortable, SortableContent } from "../ui/sortable"
import { useToolbar } from "./_toolbar-provider"
import { _SortingItem } from "./_toolbar-sorting-item"
import { _getSortingStateParser } from "./_toolbar-sorting-lib"
import {
  _OnSortRemove,
  _OnSortUpdate,
  _SortingProps,
} from "./_toolbar-sorting-types"

function Sorting<T extends DynamicObject = DynamicObject>({
  items,
  className,
}: _SortingProps<T>) {
  const id = React.useId()
  const labelId = React.useId()
  const descriptionId = React.useId()
  const addButtonRef = React.useRef<HTMLButtonElement>(null)
  const { startTransition, dictionaries, dir } = useToolbar()
  const dict = dictionaries.sorting

  const [sorting, setSorting] = useQueryState(
    "sort",
    _getSortingStateParser(items.map((item) => item.id))
      .withDefault([])
      .withOptions({ clearOnDefault: true, shallow: false, startTransition })
  )

  const onSortAdd = React.useCallback(() => {
    const firstItem = items[0]
    if (!firstItem) return

    setSorting((prevSorting) => [
      ...prevSorting,
      { id: firstItem.id, value: "asc" },
    ])
  }, [setSorting, items])

  const onSortUpdate: _OnSortUpdate = React.useCallback(
    (sortId, updates) => {
      setSorting((prevSorting) => {
        if (!prevSorting) return prevSorting
        return prevSorting.map((sort) =>
          sort.id === sortId ? { ...sort, ...updates } : sort
        )
      })
    },
    [setSorting]
  )

  const onSortRemove: _OnSortRemove = React.useCallback(
    (sortId) => {
      setSorting((prevSorting) =>
        prevSorting.filter((item) => item.id !== sortId)
      )
    },
    [setSorting]
  )

  const onSortingReset = React.useCallback(() => setSorting([]), [setSorting])

  return (
    <Sortable
      value={sorting}
      onValueChange={setSorting}
      getItemValue={(item) => item.id}
    >
      <Popover>
        <PopoverTrigger asChild>
          <Button variant="outline" size="sm" className={className}>
            <ArrowDownUp />
            {dict.triggerButtonLabel}
            {sorting.length > 0 && (
              <Badge
                variant="secondary"
                className="h-[18.24px] rounded-[3.2px] px-[5.12px] font-mono text-[10.4px] font-normal"
              >
                {sorting.length}
              </Badge>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent
          dir={dir}
          aria-labelledby={labelId}
          aria-describedby={descriptionId}
          className="flex w-full max-w-[var(--radix-popover-content-available-width)] origin-[var(--radix-popover-content-transform-origin)] flex-col gap-3.5 p-4 sm:min-w-[380px]"
        >
          <div className="flex flex-col gap-1">
            <h4 id={labelId} className="leading-none font-medium">
              {sorting.length > 0
                ? dict.popover.title.withSorting
                : dict.popover.title.noSorting}
            </h4>
            <p
              id={descriptionId}
              className={cn(
                "text-muted-foreground text-sm",
                sorting.length > 0 && "sr-only"
              )}
            >
              {dict.popover.description}
            </p>
          </div>
          {sorting.length > 0 && (
            <SortableContent asChild>
              <div
                role="list"
                className="flex max-h-[300px] flex-col gap-2 overflow-y-auto p-1"
              >
                {sorting.map((sort) => (
                  <_SortingItem
                    key={sort.id}
                    sort={sort}
                    sortItemId={`${id}-sort-${sort.id}`}
                    items={items}
                    onSortUpdate={onSortUpdate}
                    onSortRemove={onSortRemove}
                  />
                ))}
              </div>
            </SortableContent>
          )}
          <div className="flex w-full items-center gap-2">
            <Button
              size="sm"
              className="rounded"
              ref={addButtonRef}
              onClick={onSortAdd}
              disabled={items.length === 0}
            >
              {dict.popover.buttonLabels.addSorting}
            </Button>
            {sorting.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="rounded"
                onClick={onSortingReset}
              >
                {dict.popover.buttonLabels.resetSorting}
              </Button>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </Sortable>
  )
}

export { Sorting as _Sorting }
