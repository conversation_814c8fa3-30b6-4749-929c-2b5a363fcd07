import { createParser } from "nuqs/server"
import { z } from "zod/v4"

export const _sortingOrders = ["asc", "desc"] as const

const _sortingStateSchema = z.object({
  id: z.string().min(1),
  value: z.enum(["asc", "desc"]),
})

export type _SortingState = z.infer<typeof _sortingStateSchema>

export const _getSortingStateParser = (itemIds?: string[] | Set<string>) => {
  const validKeys = itemIds
    ? itemIds instanceof Set
      ? itemIds
      : new Set(itemIds)
    : null

  return createParser({
    parse: (value) => {
      try {
        const parsed = JSON.parse(value)
        const result = z.array(_sortingStateSchema).safeParse(parsed)

        if (!result.success) return null

        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {
          return null
        }

        return result.data
      } catch {
        return null
      }
    },
    serialize: (value) => JSON.stringify(value),
    eq: (a, b) =>
      a.length === b.length &&
      a.every(
        (sort, index) =>
          sort.id === b[index]?.id && sort.value === b[index]?.value
      ),
  })
}

export const _getSortingQueryParser = (sorting: _SortingState[]) => {
  const parsedSorting = sorting.map((sort) => ({
    [sort.id]: sort.value,
  }))

  return parsedSorting
}
