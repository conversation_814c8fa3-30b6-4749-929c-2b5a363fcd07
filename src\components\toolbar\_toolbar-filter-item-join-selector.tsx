import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { _FilterJoinState } from "./_toolbar-filter-lib"
import { useFilter } from "./_toolbar-filter-provider"
import { useToolbar } from "./_toolbar-provider"

/**
 * مكون لاختيار كيفية ترابط الفلتر مع الفلتر السابق
 */
function FilterJoinSelector({
  index,
  filterItemId,
}: {
  index: number
  filterItemId: string
}) {
  const { dictionaries, dir } = useToolbar()
  const { joinOperator, setJoinOperator } = useFilter()

  const dict = dictionaries.filter.filterItem.JoinSelector
  const joinOperatorListboxId = `${filterItemId}-join-operator-listbox`

  return (
    <div className="w-19 text-center">
      {index === 0 ? (
        <span className="text-muted-foreground text-sm">{dict.where}</span>
      ) : index === 1 ? (
        <Select
          dir={dir}
          value={joinOperator}
          onValueChange={(value: _FilterJoinState) => setJoinOperator(value)}
        >
          <SelectTrigger
            aria-label="Select join operator"
            aria-controls={joinOperatorListboxId}
            className="h-8 w-full rounded lowercase [&[data-size]]:h-8"
          >
            <SelectValue placeholder={dict?.[joinOperator]} />
          </SelectTrigger>
          <SelectContent
            id={joinOperatorListboxId}
            position="popper"
            className="min-w-(--radix-select-trigger-width) lowercase"
          >
            <SelectItem value={"AND"}>{dict.AND}</SelectItem>
            <SelectItem value={"OR"}>{dict.OR}</SelectItem>
          </SelectContent>
        </Select>
      ) : (
        <span className="text-muted-foreground w-full text-sm lowercase">
          {dict?.[joinOperator]}
        </span>
      )}
    </div>
  )
}

export { FilterJoinSelector as _FilterJoinSelector }
