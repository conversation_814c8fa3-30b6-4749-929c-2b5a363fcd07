import * as React from "react"

import { _FilterJoinState, _FilterState } from "./_toolbar-filter-lib"
import { _FilterItemDef } from "./_toolbar-filter-types"

export interface _FilterContextValue {
  items: _FilterItemDef[]
  filters: _FilterState[]
  joinOperator: _FilterJoinState
  setJoinOperator: (value: _FilterJoinState) => void
  onFilterRemove: (filterId: string) => void
  onFiltersReset: () => void
  onFilterAdd: () => void
  onFilterUpdate: (
    filterId: string,
    updates: Partial<Omit<_FilterState, "filterId">>
  ) => void
}

const FilterContext = React.createContext<_FilterContextValue | null>(null)

export function _FilterProvider({
  value,
  children,
}: {
  value: _FilterContextValue
  children: React.ReactNode
}) {
  return <FilterContext value={value}>{children}</FilterContext>
}

export function useFilter() {
  const context = React.useContext(FilterContext)
  if (!context) {
    throw new Error("useFilterContext must be used within a FilterProvider")
  }
  return context
}
