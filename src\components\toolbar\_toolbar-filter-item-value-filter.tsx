import * as React from "react"
import { formatDate } from "date-fns"
import { ChevronDown, SearchIcon } from "lucide-react"
import { ar } from "react-day-picker/locale"
import { toast } from "sonner"

import { cn } from "@/lib/utils"

import { Badge } from "../ui/badge"
import { Button } from "../ui/button"
import { Calendar } from "../ui/calendar"
import {
  Faceted,
  FacetedBadgeList,
  FacetedContent,
  FacetedEmpty,
  FacetedGroup,
  FacetedInput,
  FacetedItem,
  FacetedList,
  FacetedTrigger,
} from "../ui/faceted"
import { Input } from "../ui/input"
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select"
import { _FilterState } from "./_toolbar-filter-lib"
import { useFilter } from "./_toolbar-filter-provider"
import { _FilterItemDef } from "./_toolbar-filter-types"

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر
 */
function FilterValueFilter({
  filter,
  filterItemId,
  item,
}: {
  filter: _FilterState
  item: _FilterItemDef
  filterItemId: string
}) {
  const inputId = `${filterItemId}-input`

  if (filter.operator === "isEmpty" || filter.operator === "isNotEmpty") {
    return (
      <EmptyOperatorElement inputId={inputId} filter={filter} item={item} />
    )
  }

  if (filter.variant === "text" && item.variant === "text") {
    return <TextVariantElement inputId={inputId} filter={filter} item={item} />
  }

  if (filter.variant === "number" && item.variant === "number") {
    return (
      <NumberVariantElement inputId={inputId} filter={filter} item={item} />
    )
  }

  if (filter.variant === "numberRange" && item.variant === "number") {
    return (
      <NumberRangeVariantElement
        inputId={inputId}
        filter={filter}
        item={item}
      />
    )
  }

  if (
    (filter.variant === "select" && item.variant === "select") ||
    (filter.variant === "multiSelect" && item.variant === "multiSelect")
  ) {
    return (
      <SelectVariantElement inputId={inputId} filter={filter} item={item} />
    )
  }

  if (filter.variant === "boolean" && item.variant === "boolean") {
    return (
      <BooleanVariantElement inputId={inputId} filter={filter} item={item} />
    )
  }

  if (filter.variant === "date" && item.variant === "date") {
    return <DateVariantElement inputId={inputId} filter={filter} item={item} />
  }

  if (filter.variant === "dateRange" && item.variant === "date") {
    return (
      <DateRangeVariantElement inputId={inputId} filter={filter} item={item} />
    )
  }
}

export { FilterValueFilter as _FilterValueFilter }

/**
 * مكون يٌعرض عندما يكون نوع الـ operator isEmpty أو isNotEmpty
 */
function EmptyOperatorElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef
  filter: _FilterState
}) {
  return (
    <div
      id={inputId}
      role="status"
      aria-label={`${item?.label} filter is ${
        filter.operator === "isEmpty" ? "empty" : "not empty"
      }`}
      aria-live="polite"
      className="dark:bg-input/30 h-8 w-full rounded border bg-transparent"
    />
  )
}

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر عندما يكون نوعه text
 */
function TextVariantElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef & { variant: "text" }
  filter: _FilterState & { variant: "text" }
}) {
  const { onFilterUpdate } = useFilter()

  return (
    <div className={cn("relative w-full")}>
      <Input
        id={inputId}
        type={"search"}
        aria-label={`${item?.label} filter value`}
        aria-describedby={`${inputId}-description`}
        inputMode={"search"}
        placeholder={item?.placeholder}
        className={cn(
          // base
          "h-8 max-h-full w-full rounded-sm ps-8",
          // hide cancel button and decoration
          "[&::-webkit-search-cancel-button]:hidden [&::-webkit-search-decoration]:hidden"
        )}
        defaultValue={
          typeof filter.value === "string" ? filter.value : undefined
        }
        onChange={(event) =>
          onFilterUpdate(filter.filterId, {
            value: event.target.value,
          })
        }
      />
      <div
        className={cn(
          // base
          "pointer-events-none absolute start-2 bottom-0 flex h-full items-center justify-center",
          // text color
          "text-muted-foreground/60"
        )}
      >
        <SearchIcon className="size-5 shrink-0" aria-hidden="true" />
      </div>
    </div>
  )
}

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر عندما يكون نوعه number
 */
function NumberVariantElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef & { variant: "number" }
  filter: _FilterState & { variant: "number" }
}) {
  const { onFilterUpdate } = useFilter()

  const rangeMin = item.range?.min
  const rangeMax = item.range?.max

  const enableChange = React.useCallback(
    (value: string) => {
      const val = Number(value)

      if (rangeMin !== undefined && val < rangeMin) {
        toast.error(`The minimum value must be greater than ${rangeMin}`)
        return false
      }
      if (rangeMax !== undefined && val > rangeMax) {
        toast.error(`The maximum value must be less than ${rangeMax}`)
        return false
      }

      return true
    },
    [rangeMax, rangeMin]
  )

  const onValueChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value

      if (!enableChange(value)) return

      onFilterUpdate(filter.filterId, {
        value: value,
      })
    },
    [enableChange, filter.filterId, onFilterUpdate]
  )

  switch (filter.operator) {
    default:
      return (
        <div className="relative w-full">
          <Input
            id={inputId}
            type={"number"}
            aria-label={`${item?.label} filter value`}
            aria-describedby={`${inputId}-description`}
            aria-valuemin={rangeMin}
            aria-valuemax={rangeMax}
            min={rangeMin}
            max={rangeMax}
            step={item.step ?? 1}
            inputMode={"numeric"}
            placeholder={item.placeholder}
            className={cn(
              "h-8 [&>input]:w-full [&>input]:rounded-sm",
              item.unit && "[&>input]:pr-8"
            )}
            defaultValue={filter.value ?? ""}
            onChange={onValueChange}
          />
          {item?.unit && (
            <span className="bg-accent text-muted-foreground absolute top-px right-px bottom-px flex items-center rounded-r-sm px-2 text-sm">
              {item.unit}
            </span>
          )}
        </div>
      )
  }
}

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر عندما يكون نوعه numberRange
 */
function NumberRangeVariantElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef & { variant: "number" }
  filter: _FilterState & { variant: "numberRange" }
}) {
  const { onFilterUpdate } = useFilter()
  const range = item?.range

  const rangeMin = range?.min
  const rangeMax = range?.max

  const enableChange = React.useCallback(
    (value: string) => {
      const val = Number(value)

      if (rangeMin !== undefined && val < rangeMin) {
        toast.error(`The minimum value must be greater than ${rangeMin}`)
        return false
      }
      if (rangeMax !== undefined && val > rangeMax) {
        toast.error(`The maximum value must be less than ${rangeMax}`)
        return false
      }

      return true
    },
    [rangeMax, rangeMin]
  )

  const onValueChange = React.useCallback(
    (event: React.ChangeEvent<HTMLInputElement>, from: "min" | "max") => {
      const value = event.target.value

      if (!enableChange(value)) return

      if (from === "min") {
        onFilterUpdate(filter.filterId, {
          value: [value, filter.value[0]],
        })
        return
      }

      if (from === "max") {
        onFilterUpdate(filter.filterId, {
          value: [filter.value[0] ?? "", value],
        })
        return
      }
    },
    [enableChange, filter.filterId, filter.value, onFilterUpdate]
  )

  return (
    <div data-slot="range" className={cn("flex w-full items-center gap-2")}>
      <Input
        id={`${inputId}-min`}
        type="number"
        aria-label={`${item?.label} minimum value`}
        aria-valuemin={rangeMin}
        aria-valuemax={rangeMax}
        data-slot="range-min"
        inputMode="numeric"
        placeholder={rangeMin?.toString()}
        min={rangeMin}
        max={rangeMax}
        step={item.step ?? 1}
        className="h-8 w-full rounded"
        defaultValue={filter.value[0] ?? ""}
        onChange={(event) => onValueChange(event, "min")}
      />
      <span className="text-muted-foreground sr-only shrink-0">to</span>
      <Input
        id={`${inputId}-max`}
        type="number"
        aria-label={`${item?.label} maximum value`}
        aria-valuemin={rangeMin}
        aria-valuemax={rangeMax}
        data-slot="range-max"
        inputMode="numeric"
        placeholder={rangeMax?.toString()}
        min={rangeMin}
        max={rangeMax}
        step={item.step ?? 1}
        className="h-8 w-full rounded"
        defaultValue={filter.value[1] ?? ""}
        onChange={(event) => onValueChange(event, "max")}
      />
    </div>
  )
}

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر عندما يكون نوعه select أو multiSelect
 */
function SelectVariantElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef & { variant: "select" | "multiSelect" }
  filter: _FilterState & { variant: "select" | "multiSelect" }
}) {
  const { onFilterUpdate } = useFilter()
  const [open, setIsOpen] = React.useState(false)

  const inputListboxId = `${inputId}-listbox`
  const multiple = filter.variant === "multiSelect"

  const selectedValues = multiple
    ? Array.isArray(filter.value)
      ? filter.value
      : []
    : typeof filter.value === "string"
      ? filter.value
      : undefined

  return (
    <Faceted
      open={open}
      onOpenChange={setIsOpen}
      value={selectedValues}
      onValueChange={(value) => {
        onFilterUpdate(filter.filterId, {
          value,
        })
      }}
      multiple={multiple}
    >
      <FacetedTrigger asChild>
        <Button
          id={inputId}
          aria-controls={inputListboxId}
          aria-label={`${item?.label} filter value${multiple ? "s" : ""}`}
          variant="outline"
          size="sm"
          className="w-full rounded font-normal"
        >
          <FacetedBadgeList
            options={item?.options}
            placeholder={
              item?.placeholder ?? `Select option${multiple ? "s" : ""}...`
            }
          />
        </Button>
      </FacetedTrigger>
      <FacetedContent
        id={inputListboxId}
        className="w-[200px] origin-[var(--radix-popover-content-transform-origin)]"
      >
        <FacetedInput
          aria-label={`Search ${item?.label} options`}
          placeholder={item?.placeholder ?? "Search options..."}
        />
        <FacetedList>
          <FacetedEmpty>No options found.</FacetedEmpty>
          <FacetedGroup>
            {item?.options?.map((option) => (
              <FacetedItem
                className="gap-3"
                key={option.value}
                value={option.value}
              >
                {option.icon && (
                  <option.icon className="text-muted-foreground size-4" />
                )}
                <span>{option.label}</span>
                {option.count && (
                  <Badge
                    variant="outline"
                    className="ms-auto flex h-full items-center justify-center rounded-sm px-1 py-px font-mono text-xs"
                  >
                    {option.count}
                  </Badge>
                )}
              </FacetedItem>
            ))}
          </FacetedGroup>
        </FacetedList>
      </FacetedContent>
    </Faceted>
  )
}

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر عندما يكون نوعه boolean
 */
function BooleanVariantElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef & { variant: "boolean" }
  filter: _FilterState & { variant: "boolean" }
}) {
  const { onFilterUpdate } = useFilter()

  return (
    <Select
      value={String(filter.value)}
      onValueChange={(value) =>
        onFilterUpdate(filter.filterId, {
          value,
        })
      }
    >
      <SelectTrigger
        id={inputId}
        aria-controls={`${inputId}-listbox`}
        aria-label={`${item?.label} boolean filter`}
        className="h-8 w-full rounded [&[data-size]]:h-8"
      >
        <SelectValue placeholder={"Select option..."} />
      </SelectTrigger>
      <SelectContent id={`${inputId}-listbox`}>
        <SelectItem value="true">True</SelectItem>
        <SelectItem value="false">False</SelectItem>
      </SelectContent>
    </Select>
  )
}

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر عندما يكون نوعه date
 */
function DateVariantElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef & { variant: "date" }
  filter: _FilterState & { variant: "date" }
}) {
  const { onFilterUpdate } = useFilter()
  const range = item?.range

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          aria-label={`${item?.label} date filter`}
          aria-controls={`${inputId}-listbox`}
          className="w-full justify-between rounded font-normal"
        >
          {filter.value
            ? formatDate(new Date(Number(filter.value)), "yyyy-MM-dd")
            : "Select date"}
          <ChevronDown />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        id={`${inputId}-listbox`}
        className="w-auto overflow-hidden p-0"
        align="start"
      >
        <Calendar
          dir="rtl"
          locale={ar}
          mode="single"
          disabled={[
            ...(range?.min ? [{ before: new Date(range.min) }] : []),
            ...(range?.max ? [{ after: new Date(range.max) }] : []),
          ]}
          selected={new Date(Number(filter.value))}
          captionLayout="dropdown"
          onSelect={(date) => {
            onFilterUpdate(filter.filterId, {
              value: date?.getTime().toString() ?? "",
            })
          }}
        />
      </PopoverContent>
    </Popover>
  )
}

/**
 * مكون لعرض وتعديل القيمة المدخلة في الفلتر عندما يكون نوعه dateRange
 */
function DateRangeVariantElement({
  inputId,
  filter,
  item,
}: {
  inputId: string
  item: _FilterItemDef & { variant: "date" }
  filter: _FilterState & { variant: "dateRange"; operator: "isBetween" }
}) {
  const { onFilterUpdate } = useFilter()

  const from = filter.value[0] ? new Date(Number(filter.value[0])) : undefined
  const to = filter.value[1] ? new Date(Number(filter.value[1])) : undefined

  const range = item?.range

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          aria-label={`${item?.label} date range filter`}
          aria-controls={`${inputId}-listbox`}
          className="w-full justify-between rounded font-normal"
        >
          {
            <span className="truncate">
              {from && to
                ? `From -> ${formatDate(from, "yyyy/MM/dd")} - To -> ${formatDate(to, "yyyy/MM/dd")}`
                : "Pick a date"}
            </span>
          }
          <ChevronDown />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        id={`${inputId}-listbox`}
        className="w-auto overflow-hidden p-0"
        align="start"
      >
        <Calendar
          disabled={[
            ...(range?.min ? [{ before: new Date(range.min) }] : []),
            ...(range?.max ? [{ after: new Date(range.max) }] : []),
          ]}
          mode="range"
          selected={{
            from: from ?? new Date(),
            to: to ?? new Date(),
          }}
          captionLayout="dropdown"
          onSelect={(date) => {
            onFilterUpdate(filter.filterId, {
              value: [
                (date?.from?.getTime() ?? "").toString(),
                (date?.to?.getTime() ?? "").toString(),
              ],
            })
          }}
        />
      </PopoverContent>
    </Popover>
  )
}
