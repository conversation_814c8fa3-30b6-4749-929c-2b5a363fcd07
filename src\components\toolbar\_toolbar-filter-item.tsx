import * as React from "react"
import { GripVertical, Trash2 } from "lucide-react"

import { Button } from "@/components/ui/button"
import { SortableItem, SortableItemHandle } from "@/components/ui/sortable"

import { _FilterJoinSelector } from "./_toolbar-filter-item-join-selector"
import { _FilterItemSelector } from "./_toolbar-filter-item-selector"
import { _FilterValueFilter } from "./_toolbar-filter-item-value-filter"
import { _FilterState } from "./_toolbar-filter-lib"
import { _FilterOperatorSelector } from "./_toolbar-filter-operator-selector"
import { useFilter } from "./_toolbar-filter-provider"
import { useToolbar } from "./_toolbar-provider"

function FilterItem({
  index,
  filter,
  filterItemId,
}: {
  index: number
  filterItemId: string
  filter: _FilterState
}) {
  const { dir } = useToolbar()
  const { items, onFilterRemove } = useFilter()

  const item = items.find((item) => item.id === filter.id)
  if (!item) return null

  return (
    <SortableItem dir={dir} value={filter.filterId} asChild>
      <div
        role="listitem"
        id={filterItemId}
        tabIndex={-1}
        className="flex items-center gap-2"
      >
        <_FilterJoinSelector index={index} filterItemId={filterItemId} />
        <_FilterItemSelector
          filterItemId={filterItemId}
          filter={filter}
          item={item}
        />
        <_FilterOperatorSelector filter={filter} filterItemId={filterItemId} />
        <div className="min-w-36 flex-1">
          <_FilterValueFilter
            filterItemId={filterItemId}
            filter={filter}
            item={item}
          />
        </div>
        <Button
          aria-controls={filterItemId}
          variant="outline"
          size="icon"
          className="size-8 rounded"
          onClick={() => onFilterRemove(filter.filterId)}
        >
          <Trash2 />
        </Button>
        <SortableItemHandle asChild>
          <Button variant="outline" size="icon" className="size-8 rounded">
            <GripVertical />
          </Button>
        </SortableItemHandle>
      </div>
    </SortableItem>
  )
}

export { FilterItem as _FilterItem }
